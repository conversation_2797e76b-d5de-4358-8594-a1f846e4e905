import { motion } from 'framer-motion';
import { FiArrowDown, FiDownload, FiPlay } from 'react-icons/fi';

const FloatingShape = () => {
  return (
    <motion.div
      className="w-64 h-64 rounded-full bg-gradient-to-r from-primary-500 to-secondary-600 opacity-30"
      animate={{
        scale: [1, 1.1, 1],
        rotate: [0, 180, 360],
        x: [0, 50, 0],
        y: [0, -30, 0],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      style={{
        background: 'radial-gradient(circle at 30% 30%, #0ea5e9, #0284c7, #0369a1)',
        filter: 'blur(1px)',
      }}
    />
  );
};

const Home = () => {
  const scrollToNext = () => {
    const aboutSection = document.querySelector('#about');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="min-h-screen relative overflow-hidden bg-gradient-to-br from-primary-900 via-secondary-900 to-accent-900">
      {/* 3D-like Background */}
      <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
        <FloatingShape />
        <motion.div
          className="absolute w-96 h-96 rounded-full bg-gradient-to-r from-secondary-500 to-accent-500 opacity-20"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, -180, -360],
            x: [0, -100, 0],
            y: [0, 50, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          style={{
            background: 'radial-gradient(circle at 70% 70%, #d946ef, #c026d3, #f97316)',
            filter: 'blur(2px)',
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen">
        <div className="container-custom px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            {/* Greeting */}
            <motion.p
              className="text-lg md:text-xl text-gray-300 mb-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              Hello, I'm
            </motion.p>

            {/* Name */}
            <motion.h1
              className="text-5xl md:text-7xl font-bold text-white mb-6 font-display"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.7, type: "spring", stiffness: 100 }}
            >
              <span className="text-gradient">Hari Raj</span>
            </motion.h1>

            {/* Profession */}
            <motion.div
              className="text-xl md:text-2xl text-gray-300 mb-8 space-y-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 }}
            >
              <p>Digital Marketing Specialist</p>
              <p className="text-primary-400">• Web Developer • IoT Enthusiast</p>
            </motion.div>

            {/* Description */}
            <motion.p
              className="text-lg text-gray-400 max-w-2xl mx-auto mb-12 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
            >
              Passionate about creating innovative digital solutions with 2+ years of experience 
              in marketing, full-stack development, and IoT projects. Let's build something amazing together!
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.4 }}
            >
              <motion.button
                className="btn-primary flex items-center space-x-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => document.querySelector('#projects')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <FiPlay className="w-5 h-5" />
                <span>View My Work</span>
              </motion.button>
              
              <motion.button
                className="btn-secondary flex items-center space-x-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FiDownload className="w-5 h-5" />
                <span>Download CV</span>
              </motion.button>
            </motion.div>

            {/* Scroll Indicator */}
            <motion.div
              className="flex flex-col items-center cursor-pointer"
              onClick={scrollToNext}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
              whileHover={{ scale: 1.1 }}
            >
              <p className="text-gray-400 text-sm mb-2">Scroll to explore</p>
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <FiArrowDown className="w-6 h-6 text-primary-400" />
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-accent-400 rounded-full opacity-40"
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
            animate={{
              y: [null, -100],
              opacity: [0.4, 0],
            }}
            transition={{
              duration: Math.random() * 4 + 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>
    </section>
  );
};

export default Home;
