import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { FiCode, FiTrendingUp, FiCpu, FiImage } from 'react-icons/fi';

const About = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const skills = [
    { name: 'Web Development', level: 90, icon: FiCode, color: 'bg-blue-500' },
    { name: 'Digital Marketing', level: 85, icon: FiTrendingUp, color: 'bg-green-500' },
    { name: 'IoT Development', level: 80, icon: FiCpu, color: 'bg-purple-500' },
    { name: 'Graphic Design', level: 75, icon: FiImage, color: 'bg-pink-500' },
  ];

  const timeline = [
    {
      year: '2022',
      title: 'Started Digital Marketing',
      description: 'Began my journey in digital marketing, learning SEO, social media, and content strategy.',
    },
    {
      year: '2023',
      title: 'Full-Stack Development',
      description: 'Expanded into web development, mastering React, Node.js, and database management.',
    },
    {
      year: '2024',
      title: 'IoT Projects',
      description: 'Developed smart battery charging system and AI chatbot attendance system.',
    },
    {
      year: 'Present',
      title: 'Freelance & Growth',
      description: 'Currently working on diverse projects and continuously learning new technologies.',
    },
  ];

  return (
    <section id="about" className="section-padding bg-white">
      <div className="container-custom" ref={ref}>
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 font-display">
            About <span className="text-gradient">Me</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Passionate about technology and innovation, I bring ideas to life through code and creativity.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Image and Stats */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {/* Profile Image */}
            <div className="relative mb-8">
              <div className="w-80 h-80 mx-auto bg-gradient-to-br from-primary-400 to-secondary-600 rounded-2xl flex items-center justify-center">
                <div className="w-72 h-72 bg-gray-200 rounded-xl flex items-center justify-center">
                  <span className="text-6xl text-gray-400">📸</span>
                </div>
              </div>
              {/* Floating Elements */}
              <motion.div
                className="absolute -top-4 -right-4 w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                <FiCode className="w-8 h-8 text-primary-600" />
              </motion.div>
              <motion.div
                className="absolute -bottom-4 -left-4 w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center"
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <FiCpu className="w-6 h-6 text-secondary-600" />
              </motion.div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-2xl font-bold text-primary-600">2+</h3>
                <p className="text-gray-600">Years Experience</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-2xl font-bold text-primary-600">15+</h3>
                <p className="text-gray-600">Projects Done</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-2xl font-bold text-primary-600">10+</h3>
                <p className="text-gray-600">Happy Clients</p>
              </div>
            </div>
          </motion.div>

          {/* Right Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {/* Bio */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">My Story</h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                Hi, I'm Hari Raj, a passionate digital professional with over 2 years of experience in creating
                innovative solutions that bridge technology and business needs. My journey started
                with digital marketing, where I learned the art of connecting with audiences and
                driving engagement.
              </p>
              <p className="text-gray-600 leading-relaxed">
                As I delved deeper into the tech world, I expanded my skills to include full-stack
                web development and IoT projects. I believe in continuous learning and staying
                updated with the latest technologies to deliver cutting-edge solutions.
              </p>
            </div>

            {/* Skills */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Skills & Expertise</h3>
              <div className="space-y-4">
                {skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: 20 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ delay: 0.6 + index * 0.1 }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <skill.icon className="w-5 h-5 text-primary-600" />
                        <span className="font-medium text-gray-900">{skill.name}</span>
                      </div>
                      <span className="text-gray-600">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        className={`h-2 rounded-full ${skill.color}`}
                        initial={{ width: 0 }}
                        animate={inView ? { width: `${skill.level}%` } : {}}
                        transition={{ delay: 0.8 + index * 0.1, duration: 1 }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Timeline */}
        <motion.div
          className="mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">My Journey</h3>
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary-200"></div>
            
            {timeline.map((item, index) => (
              <motion.div
                key={index}
                className={`flex items-center mb-8 ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                animate={inView ? { opacity: 1, x: 0 } : {}}
                transition={{ delay: 1 + index * 0.2 }}
              >
                <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8'}`}>
                  <div className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                    <span className="text-primary-600 font-bold text-lg">{item.year}</span>
                    <h4 className="font-bold text-gray-900 text-xl mb-2">{item.title}</h4>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </div>
                <div className="w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg z-10"></div>
                <div className="w-1/2"></div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
