import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5175,
    force: true
  },
  optimizeDeps: {
    force: true,
    include: [
      '@react-three/fiber',
      '@react-three/drei',
      'three',
      'framer-motion',
      'react-icons/fi'
    ]
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'react-three': ['@react-three/fiber', '@react-three/drei', 'three']
        }
      }
    }
  }
})
