import { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { FiExternalLink, FiGithub, FiCode, FiTrendingUp, FiCpu, FiImage } from 'react-icons/fi';

const Projects = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activeFilter, setActiveFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: 'Civix - Civic Engagement Platform',
      description: 'A comprehensive civic engagement platform that connects citizens with their local government and community initiatives. Features real-time updates, community forums, and civic participation tools.',
      image: '🏛️',
      category: 'web',
      technologies: ['React', 'Node.js', 'MongoDB', 'Express.js'],
      features: ['Civic engagement', 'Community forums', 'Real-time updates', 'Government integration'],
      github: '#',
      demo: 'https://civix.in/',
      color: 'from-blue-500 to-indigo-600',
    },
    {
      id: 2,
      title: 'Smart Battery Charging System',
      description: 'IoT-based intelligent battery charging system with real-time monitoring, automatic cutoff, and mobile app integration.',
      image: '🔋',
      category: 'iot',
      technologies: ['Arduino', 'React Native', 'Firebase', 'Sensors'],
      features: ['Real-time monitoring', 'Automatic cutoff', 'Mobile alerts', 'Data analytics'],
      github: '#',
      demo: '#',
      color: 'from-purple-400 to-purple-600',
    },
    {
      id: 3,
      title: 'AI Chatbot Attendance System',
      description: 'Intelligent attendance management system using AI chatbot for automated check-ins and reporting.',
      image: '🤖',
      category: 'web',
      technologies: ['React', 'Node.js', 'OpenAI API', 'MongoDB'],
      features: ['AI-powered chat', 'Automated attendance', 'Real-time reports', 'Admin dashboard'],
      github: '#',
      demo: '#',
      color: 'from-blue-400 to-blue-600',
    },
    {
      id: 4,
      title: 'E-commerce Marketing Campaign',
      description: 'Comprehensive digital marketing campaign that increased client sales by 150% through strategic SEO and social media.',
      image: '📈',
      category: 'marketing',
      technologies: ['Google Analytics', 'Facebook Ads', 'SEO Tools', 'Content Strategy'],
      features: ['150% sales increase', 'SEO optimization', 'Social media growth', 'ROI tracking'],
      github: '#',
      demo: '#',
      color: 'from-green-400 to-green-600',
    },
    {
      id: 5,
      title: 'Brand Identity Design',
      description: 'Complete brand identity design including logo, color palette, typography, and brand guidelines for a tech startup.',
      image: '🎨',
      category: 'design',
      technologies: ['Adobe Illustrator', 'Figma', 'Photoshop', 'Brand Strategy'],
      features: ['Logo design', 'Brand guidelines', 'Color palette', 'Typography system'],
      github: '#',
      demo: '#',
      color: 'from-pink-400 to-pink-600',
    },
    {
      id: 6,
      title: 'Restaurant Management App',
      description: 'Full-stack restaurant management application with order tracking, inventory management, and customer feedback system.',
      image: '🍽️',
      category: 'web',
      technologies: ['React', 'Express.js', 'PostgreSQL', 'Stripe API'],
      features: ['Order management', 'Inventory tracking', 'Payment integration', 'Customer reviews'],
      github: '#',
      demo: '#',
      color: 'from-orange-400 to-orange-600',
    },
    {
      id: 7,
      title: 'Home Automation System',
      description: 'IoT-based home automation system controlling lights, temperature, and security with voice commands and mobile app.',
      image: '🏠',
      category: 'iot',
      technologies: ['Raspberry Pi', 'Python', 'React Native', 'MQTT'],
      features: ['Voice control', 'Mobile app', 'Security monitoring', 'Energy optimization'],
      github: '#',
      demo: '#',
      color: 'from-indigo-400 to-indigo-600',
    },
  ];

  const filters = [
    { key: 'all', label: 'All Projects', icon: FiCode },
    { key: 'web', label: 'Web Development', icon: FiCode },
    { key: 'iot', label: 'IoT Projects', icon: FiCpu },
    { key: 'marketing', label: 'Digital Marketing', icon: FiTrendingUp },
    { key: 'design', label: 'Design', icon: FiImage },
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <section id="projects" className="section-padding bg-white">
      <div className="container-custom" ref={ref}>
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 font-display">
            My <span className="text-gradient">Projects</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            A showcase of my recent work across web development, IoT, digital marketing, and design.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {filters.map((filter, index) => (
            <motion.button
              key={filter.key}
              onClick={() => setActiveFilter(filter.key)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeFilter === filter.key
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <filter.icon className="w-4 h-4" />
              <span>{filter.label}</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden"
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 + index * 0.1 }}
              layout
            >
              {/* Project Image/Icon */}
              <div className={`h-48 bg-gradient-to-br ${project.color} flex items-center justify-center text-6xl group-hover:scale-105 transition-transform duration-300`}>
                {project.image}
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                  {project.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.map((tech) => (
                    <span
                      key={tech}
                      className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Features */}
                <div className="space-y-1 mb-6">
                  {project.features.map((feature) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <div className={`w-2 h-2 bg-gradient-to-r ${project.color} rounded-full`}></div>
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <motion.a
                    href={project.demo}
                    className="flex-1 flex items-center justify-center space-x-2 py-2 px-4 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <FiExternalLink className="w-4 h-4" />
                    <span>Demo</span>
                  </motion.a>
                  <motion.a
                    href={project.github}
                    className="flex items-center justify-center p-2 border-2 border-gray-300 text-gray-700 rounded-lg hover:border-primary-600 hover:text-primary-600 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <FiGithub className="w-5 h-5" />
                  </motion.a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Interested in Working Together?</h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            I'm always excited to take on new challenges and create innovative solutions.
          </p>
          <motion.button
            className="btn-primary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' })}
          >
            Start a Project
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
