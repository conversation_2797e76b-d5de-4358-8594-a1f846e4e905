@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    @apply font-sans;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: #ffffff;
    color: #1f2937;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-300;
  }

  .section-padding {
    @apply py-20 px-4 sm:px-6 lg:px-8;
  }

  .container-custom {
    @apply max-w-7xl mx-auto;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
}
