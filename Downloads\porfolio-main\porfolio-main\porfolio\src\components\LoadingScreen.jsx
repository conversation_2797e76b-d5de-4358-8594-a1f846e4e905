import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Simplified 3D-like animation without Three.js dependencies
const AnimatedSphere = () => {
  return (
    <motion.div
      className="w-32 h-32 rounded-full bg-gradient-to-r from-primary-500 to-secondary-600 opacity-80"
      animate={{
        scale: [1, 1.2, 1],
        rotate: [0, 360],
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      style={{
        background: 'radial-gradient(circle at 30% 30%, #0ea5e9, #0284c7, #c026d3)',
        boxShadow: '0 0 50px rgba(14, 165, 233, 0.5)',
      }}
    />
  );
};

const LoadingScreen = ({ onLoadingComplete }) => {
  const [progress, setProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer);
          setIsComplete(true);
          setTimeout(() => {
            onLoadingComplete();
          }, 1000);
          return 100;
        }
        return prev + 2;
      });
    }, 50);

    return () => clearInterval(timer);
  }, [onLoadingComplete]);

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-primary-900 via-secondary-900 to-accent-900"
      initial={{ opacity: 1 }}
      animate={{ opacity: isComplete ? 0 : 1 }}
      transition={{ duration: 1, delay: isComplete ? 0.5 : 0 }}
    >
      {/* 3D-like Background */}
      <div className="absolute inset-0 flex items-center justify-center">
        <AnimatedSphere />
      </div>

      {/* Loading Content */}
      <div className="relative z-10 text-center">
        {/* Animated Logo/Name */}
        <motion.div
          className="mb-8"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 1, type: "spring", stiffness: 100 }}
        >
          <h1 className="text-6xl font-bold text-white mb-2 font-display">
            <span className="text-gradient">Hari Raj</span>
          </h1>
          <p className="text-xl text-gray-300">Digital Marketing • Web Development • IoT</p>
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          className="w-80 h-2 bg-gray-700 rounded-full overflow-hidden mx-auto mb-4"
          initial={{ width: 0 }}
          animate={{ width: 320 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <motion.div
            className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.1 }}
          />
        </motion.div>

        {/* Progress Text */}
        <motion.p
          className="text-gray-400 text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          {progress < 100 ? `Loading... ${progress}%` : 'Welcome!'}
        </motion.p>

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-primary-400 rounded-full opacity-60"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
              }}
              animate={{
                y: [null, -100],
                opacity: [0.6, 0],
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default LoadingScreen;
